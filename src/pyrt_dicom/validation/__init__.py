# Copyright (C) 2024 Pirate DICOM Contributors

"""
Validation utilities for RT DICOM creation.

This module provides clinical and technical validation for DICOM RT objects,
ensuring both DICOM standard compliance and clinical safety.
"""

from .geometric import (
    GeometricValidator,
    validate_coordinate_bounds,
    validate_geometric_consistency,
    validate_structure_geometry,
    validate_coordinate_transformation_inputs,
    validate_image_orientation_and_position,
    check_contour_closure,
    validate_3d_array_shape_convention,
    enforce_3d_array_shape_convention,
    validate_dose_ct_alignment,
    validate_dose_spatial_bounds,
    CLINICAL_GEOMETRIC_LIMITS,
)

from .patient import (
    PatientInfoValidator,
    validate_patient_info,
    validate_dicom_date,
    validate_patient_id,
    PATIENT_INFO_CONSTRAINTS,
)

from .dicom_tags import (
    DicomTagValidator,
    validate_dicom_tag_value,
    validate_vr_constraints,
    DICOM_VR_CONSTRAINTS,
)

from .clinical import (
    ClinicalValidator,
    ValidationResult,
    ValidationLevel,
    validate_clinical_parameters,
    format_validation_report,
)

from .dicom_compliance import (
    DicomComplianceValidator,
    IODType,
    DicomElement,
    validate_dicom_compliance,
    generate_conformance_statement,
)

__all__ = [
    # Geometric validation
    "GeometricValidator",
    "validate_coordinate_bounds",
    "validate_geometric_consistency",
    "validate_structure_geometry",
    "validate_coordinate_transformation_inputs",
    "validate_image_orientation_and_position",
    "check_contour_closure",
    "validate_3d_array_shape_convention",
    "enforce_3d_array_shape_convention",
    "CLINICAL_GEOMETRIC_LIMITS",
    # Patient information validation
    "PatientInfoValidator",
    "validate_patient_info",
    "validate_dicom_date",
    "validate_patient_id",
    "PATIENT_INFO_CONSTRAINTS",
    # DICOM tag validation
    "DicomTagValidator",
    "validate_dicom_tag_value",
    "validate_vr_constraints",
    "DICOM_VR_CONSTRAINTS",
    # Clinical validation
    "ClinicalValidator",
    "ValidationResult",
    "ValidationLevel",
    "validate_clinical_parameters",
    "format_validation_report",
    # DICOM compliance validation
    "DicomComplianceValidator",
    "IODType",
    "DicomElement",
    "validate_dicom_compliance",
    "generate_conformance_statement",
]
