"""
Multi-Vendor TPS Compatibility Testing Framework for Task 1.4.3.

This module provides comprehensive testing for RT Dose DICOM file compatibility
across multiple treatment planning systems (TPS). Tests validate DICOM IOD
compliance, vendor-specific requirements, and clinical workflows.

Supported TPS Systems:
- Varian Eclipse (ARIA oncology information system)
- Elekta Monaco/XiO treatment planning systems  
- RaySearch RayStation treatment planning system
- Generic DICOM-RT compliance validation

Test Categories:
- DICOM IOD compliance validation
- Vendor-specific tag requirements
- Clinical workflow compatibility
- Import/export format validation
- Multi-vendor interoperability testing
"""

import pytest
import numpy as np
import tempfile
import shutil
from pathlib import Path
import pydicom
from pydicom.dataset import Dataset
from typing import Dict, List, Optional, Tuple

from pyrt_dicom.core.ct_series import CTSeries
from pyrt_dicom.core.rt_struct import RTStructureSet
from pyrt_dicom.core.rt_dose import RTDose
from pyrt_dicom.validation.dicom_compliance import DicomComplianceValidator
from pyrt_dicom.utils.exceptions import ValidationError


@pytest.fixture
def temp_vendor_dir():
    """Create temporary directory for vendor compatibility testing."""
    temp_dir = tempfile.mkdtemp(prefix="vendor_compatibility_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def clinical_dataset_suite():
    """Create comprehensive clinical dataset for TPS compatibility testing."""
    # Create realistic clinical CT data
    ct_array = np.random.randint(-1000, 3000, size=(30, 256, 256), dtype=np.int16)
    
    # Add realistic tissue HU values
    ct_array[:, :30, :] = -1000  # Air
    ct_array[:, 30:220, 30:220] = np.random.randint(0, 100, size=(30, 190, 190))  # Soft tissue
    ct_array[:, 100:150, 100:150] = np.random.randint(500, 1500, size=(30, 50, 50))  # Bone
    
    # Create realistic dose distribution
    dose_array = np.zeros((30, 256, 256), dtype=np.float64)
    center_z, center_y, center_x = 15, 128, 128
    z_indices, y_indices, x_indices = np.mgrid[0:30, 0:256, 0:256]
    
    distance = np.sqrt(
        ((z_indices - center_z) * 2.5)**2 +
        ((y_indices - center_y) * 1.0)**2 +
        ((x_indices - center_x) * 1.0)**2
    )
    
    dose_array = 70.0 * np.exp(-distance / 40.0)
    dose_array[distance > 80] = 0.0
    dose_array = np.maximum(0, dose_array + np.random.normal(0, 0.1, dose_array.shape))
    
    # Create structure masks
    masks = {}
    
    # PTV
    ptv_mask = np.zeros((30, 256, 256), dtype=bool)
    ptv_mask[10:20, 110:150, 110:150] = True
    masks['PTV_7000'] = ptv_mask
    
    # OARs
    rectum_mask = np.zeros((30, 256, 256), dtype=bool)
    rectum_mask[10:20, 90:110, 120:140] = True
    masks['Rectum'] = rectum_mask
    
    bladder_mask = np.zeros((30, 256, 256), dtype=bool)
    bladder_mask[10:20, 150:180, 110:150] = True
    masks['Bladder'] = bladder_mask
    
    return {
        'ct_array': ct_array,
        'dose_array': dose_array,
        'structure_masks': masks,
        'geometric_params': {
            'pixel_spacing': [1.0, 1.0],
            'slice_thickness': 2.5,
            'image_position': [-128.0, -128.0, -37.5],
            'image_orientation': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
            'patient_position': 'HFS'
        }
    }


class VendorCompatibilityValidator:
    """Validator for vendor-specific TPS compatibility requirements."""
    
    # Vendor-specific requirements based on TPS documentation
    VENDOR_REQUIREMENTS = {
        'varian_eclipse': {
            'required_tags': [
                'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID', 'FrameOfReferenceUID',
                'DoseUnits', 'DoseType', 'DoseSummationType', 'DoseGridScaling'
            ],
            'preferred_dose_units': ['GY'],
            'supported_dose_types': ['PHYSICAL', 'EFFECTIVE'],
            'max_dose_grid_size': [512, 512, 512],
            'min_pixel_spacing': 0.5,  # mm
            'max_pixel_spacing': 10.0,  # mm
            'coordinate_system': 'PATIENT',  # DICOM LPS
            'transfer_syntax': '1.2.840.10008.1.2.1'  # Explicit VR Little Endian
        },
        'elekta_monaco': {
            'required_tags': [
                'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID', 'FrameOfReferenceUID',
                'DoseUnits', 'DoseType', 'DoseSummationType', 'DoseGridScaling',
                'ImagePositionPatient', 'ImageOrientationPatient'
            ],
            'preferred_dose_units': ['GY'],
            'supported_dose_types': ['PHYSICAL'],
            'max_dose_grid_size': [256, 256, 200],
            'min_pixel_spacing': 1.0,  # mm
            'max_pixel_spacing': 5.0,  # mm
            'coordinate_system': 'PATIENT',
            'transfer_syntax': '1.2.840.10008.1.2'  # Implicit VR Little Endian
        },
        'raystation': {
            'required_tags': [
                'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID', 'FrameOfReferenceUID',
                'DoseUnits', 'DoseType', 'DoseSummationType', 'DoseGridScaling',
                'PixelSpacing', 'SliceThickness'
            ],
            'preferred_dose_units': ['GY'],
            'supported_dose_types': ['PHYSICAL', 'EFFECTIVE', 'ERROR'],
            'max_dose_grid_size': [1024, 1024, 1024],
            'min_pixel_spacing': 0.1,  # mm
            'max_pixel_spacing': 20.0,  # mm
            'coordinate_system': 'PATIENT',
            'transfer_syntax': '1.2.840.10008.1.2.1'  # Explicit VR Little Endian
        },
        'generic_dicom': {
            'required_tags': [
                'SOPClassUID', 'SOPInstanceUID', 'StudyInstanceUID', 'SeriesInstanceUID',
                'PatientID', 'FrameOfReferenceUID', 'Modality', 'DoseUnits', 'DoseType',
                'DoseSummationType', 'DoseGridScaling'
            ],
            'preferred_dose_units': ['GY', 'RELATIVE'],
            'supported_dose_types': ['PHYSICAL', 'EFFECTIVE', 'ERROR'],
            'max_dose_grid_size': [2048, 2048, 2048],
            'min_pixel_spacing': 0.01,  # mm
            'max_pixel_spacing': 50.0,  # mm
            'coordinate_system': 'PATIENT',
            'transfer_syntax': '1.2.840.10008.1.2.1'
        }
    }
    
    def validate_vendor_compatibility(self, dataset: Dataset, vendor: str) -> List[Dict[str, str]]:
        """Validate DICOM dataset against vendor-specific requirements.
        
        Args:
            dataset: DICOM dataset to validate
            vendor: Target vendor system ('varian_eclipse', 'elekta_monaco', 'raystation', 'generic_dicom')
            
        Returns:
            List of validation issues, empty if fully compatible
        """
        if vendor not in self.VENDOR_REQUIREMENTS:
            return [{'type': 'error', 'message': f'Unknown vendor: {vendor}'}]
        
        requirements = self.VENDOR_REQUIREMENTS[vendor]
        issues = []
        
        # Check required tags
        for tag in requirements['required_tags']:
            if not hasattr(dataset, tag):
                issues.append({
                    'type': 'error',
                    'message': f'Missing required tag for {vendor}: {tag}',
                    'vendor': vendor,
                    'tag': tag
                })
            elif getattr(dataset, tag) is None:
                issues.append({
                    'type': 'warning',
                    'message': f'Required tag {tag} is None for {vendor}',
                    'vendor': vendor,
                    'tag': tag
                })
        
        # Check dose units
        if hasattr(dataset, 'DoseUnits'):
            dose_units = dataset.DoseUnits
            if dose_units not in requirements['preferred_dose_units']:
                issues.append({
                    'type': 'warning',
                    'message': f'Dose units {dose_units} not preferred for {vendor}. Preferred: {requirements["preferred_dose_units"]}',
                    'vendor': vendor,
                    'actual': dose_units,
                    'preferred': requirements['preferred_dose_units']
                })
        
        # Check dose type
        if hasattr(dataset, 'DoseType'):
            dose_type = dataset.DoseType
            if dose_type not in requirements['supported_dose_types']:
                issues.append({
                    'type': 'error',
                    'message': f'Dose type {dose_type} not supported by {vendor}. Supported: {requirements["supported_dose_types"]}',
                    'vendor': vendor,
                    'actual': dose_type,
                    'supported': requirements['supported_dose_types']
                })
        
        # Check dose grid size limits
        if hasattr(dataset, 'pixel_array'):
            grid_shape = dataset.pixel_array.shape
            max_shape = requirements['max_dose_grid_size']
            
            for i, (actual, max_size) in enumerate(zip(grid_shape, max_shape)):
                if actual > max_size:
                    axis_name = ['Z', 'Y', 'X'][i] if len(grid_shape) == 3 else str(i)
                    issues.append({
                        'type': 'error',
                        'message': f'Dose grid {axis_name} dimension {actual} exceeds {vendor} limit {max_size}',
                        'vendor': vendor,
                        'axis': axis_name,
                        'actual': actual,
                        'limit': max_size
                    })
        
        # Check pixel spacing
        if hasattr(dataset, 'PixelSpacing'):
            pixel_spacing = dataset.PixelSpacing
            min_spacing = requirements['min_pixel_spacing']
            max_spacing = requirements['max_pixel_spacing']
            
            for spacing in pixel_spacing:
                if spacing < min_spacing or spacing > max_spacing:
                    issues.append({
                        'type': 'warning',
                        'message': f'Pixel spacing {spacing}mm outside {vendor} recommended range [{min_spacing}-{max_spacing}]mm',
                        'vendor': vendor,
                        'actual': spacing,
                        'min_recommended': min_spacing,
                        'max_recommended': max_spacing
                    })
        
        return issues


class TestVarianEclipseCompatibility:
    """Test compatibility with Varian Eclipse treatment planning system."""
    
    def test_varian_eclipse_rt_dose_compatibility(self, clinical_dataset_suite, temp_vendor_dir):
        """Test RT Dose compatibility with Varian Eclipse requirements."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={
                'PatientID': 'VARIAN_001',
                'PatientName': 'Eclipse^Compatibility^Test'
            }
        )
        
        ct_dir = temp_vendor_dir / "varian_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose optimized for Varian Eclipse
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',  # Varian preferred
            dose_type='PHYSICAL',  # Varian supported
            summation_type='PLAN',
            patient_info={
                'PatientID': 'VARIAN_001',
                'PatientName': 'Eclipse^Compatibility^Test'
            }
        )
        
        # Save and validate
        dose_path = temp_vendor_dir / "varian_dose.dcm"
        rt_dose.save(dose_path)
        
        # Load and validate against Varian requirements
        reloaded_dose = pydicom.dcmread(dose_path)
        validator = VendorCompatibilityValidator()
        issues = validator.validate_vendor_compatibility(reloaded_dose, 'varian_eclipse')
        
        # Filter out warnings, focus on errors
        errors = [issue for issue in issues if issue['type'] == 'error']
        assert len(errors) == 0, f"Varian Eclipse compatibility errors: {errors}"
        
        # Verify specific Varian requirements
        assert reloaded_dose.DoseUnits == 'GY', "Varian Eclipse requires GY dose units"
        assert reloaded_dose.DoseType == 'PHYSICAL', "Varian Eclipse requires PHYSICAL dose type"
        assert hasattr(reloaded_dose, 'DoseGridScaling'), "Varian Eclipse requires DoseGridScaling"
        assert reloaded_dose.DoseGridScaling > 0, "DoseGridScaling must be positive"
        
        # Verify coordinate system compatibility
        assert hasattr(reloaded_dose, 'ImagePositionPatient'), "Varian requires ImagePositionPatient"
        assert hasattr(reloaded_dose, 'ImageOrientationPatient'), "Varian requires ImageOrientationPatient"
        
        print(f"✅ Varian Eclipse compatibility validated:")
        print(f"   - All required tags present")
        print(f"   - Dose units: {reloaded_dose.DoseUnits}")
        print(f"   - Dose type: {reloaded_dose.DoseType}")
        print(f"   - Grid size: {reloaded_dose.pixel_array.shape}")
        print(f"   - Warnings: {len([i for i in issues if i['type'] == 'warning'])}")
    
    def test_varian_eclipse_multi_object_workflow(self, clinical_dataset_suite, temp_vendor_dir):
        """Test complete Varian Eclipse workflow with CT + RTSTRUCT + RTDOSE."""
        
        # Create CT series
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': 'VARIAN_WORKFLOW_001', 'PatientName': 'Eclipse^Workflow^Test'}
        )
        
        ct_dir = temp_vendor_dir / "varian_workflow_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Structure Set
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=clinical_dataset_suite['structure_masks'],
            patient_info={'PatientID': 'VARIAN_WORKFLOW_001', 'PatientName': 'Eclipse^Workflow^Test'}
        )
        
        # Create RT Dose
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            patient_info={'PatientID': 'VARIAN_WORKFLOW_001', 'PatientName': 'Eclipse^Workflow^Test'}
        )
        
        # Save all objects
        struct_path = temp_vendor_dir / "varian_workflow_struct.dcm"
        dose_path = temp_vendor_dir / "varian_workflow_dose.dcm"
        
        rt_struct.save(struct_path)
        rt_dose.save(dose_path)
        
        # Validate workflow integrity
        reloaded_struct = pydicom.dcmread(struct_path)
        reloaded_dose = pydicom.dcmread(dose_path)
        
        # Verify UID consistency (critical for Varian Eclipse)
        assert reference_ct.StudyInstanceUID == reloaded_struct.StudyInstanceUID
        assert reference_ct.StudyInstanceUID == reloaded_dose.StudyInstanceUID
        assert reference_ct.FrameOfReferenceUID == reloaded_struct.FrameOfReferenceUID
        assert reference_ct.FrameOfReferenceUID == reloaded_dose.FrameOfReferenceUID
        
        # Verify patient consistency
        assert reference_ct.PatientID == reloaded_struct.PatientID == reloaded_dose.PatientID
        
        print(f"✅ Varian Eclipse multi-object workflow validated:")
        print(f"   - Study UID consistency: {reference_ct.StudyInstanceUID}")
        print(f"   - Frame of Reference UID consistency: {reference_ct.FrameOfReferenceUID}")
        print(f"   - Patient ID consistency: {reference_ct.PatientID}")


class TestElektaMonacoCompatibility:
    """Test compatibility with Elekta Monaco treatment planning system."""
    
    def test_elekta_monaco_rt_dose_compatibility(self, clinical_dataset_suite, temp_vendor_dir):
        """Test RT Dose compatibility with Elekta Monaco requirements."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': 'ELEKTA_001', 'PatientName': 'Monaco^Compatibility^Test'}
        )
        
        ct_dir = temp_vendor_dir / "elekta_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose optimized for Elekta Monaco
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',  # Elekta preferred
            dose_type='PHYSICAL',  # Elekta supported
            summation_type='PLAN',
            patient_info={'PatientID': 'ELEKTA_001', 'PatientName': 'Monaco^Compatibility^Test'}
        )
        
        # Save and validate
        dose_path = temp_vendor_dir / "elekta_dose.dcm"
        rt_dose.save(dose_path)
        
        # Load and validate against Elekta requirements
        reloaded_dose = pydicom.dcmread(dose_path)
        validator = VendorCompatibilityValidator()
        issues = validator.validate_vendor_compatibility(reloaded_dose, 'elekta_monaco')
        
        # Filter out warnings, focus on errors
        errors = [issue for issue in issues if issue['type'] == 'error']
        assert len(errors) == 0, f"Elekta Monaco compatibility errors: {errors}"
        
        # Verify specific Elekta requirements
        assert reloaded_dose.DoseUnits == 'GY', "Elekta Monaco requires GY dose units"
        assert reloaded_dose.DoseType == 'PHYSICAL', "Elekta Monaco requires PHYSICAL dose type"
        
        # Verify dose grid size within Elekta limits
        dose_shape = reloaded_dose.pixel_array.shape
        elekta_limits = VendorCompatibilityValidator.VENDOR_REQUIREMENTS['elekta_monaco']['max_dose_grid_size']
        
        for i, (actual, limit) in enumerate(zip(dose_shape, elekta_limits)):
            assert actual <= limit, f"Dose grid dimension {i} ({actual}) exceeds Elekta limit ({limit})"
        
        print(f"✅ Elekta Monaco compatibility validated:")
        print(f"   - Dose grid size: {dose_shape} (within limits: {elekta_limits})")
        print(f"   - All required geometric tags present")
        print(f"   - Warnings: {len([i for i in issues if i['type'] == 'warning'])}")


class TestRayStationCompatibility:
    """Test compatibility with RaySearch RayStation treatment planning system."""
    
    def test_raystation_rt_dose_compatibility(self, clinical_dataset_suite, temp_vendor_dir):
        """Test RT Dose compatibility with RayStation requirements."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': 'RAYSTATION_001', 'PatientName': 'RayStation^Compatibility^Test'}
        )
        
        ct_dir = temp_vendor_dir / "raystation_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose optimized for RayStation
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',  # RayStation preferred
            dose_type='PHYSICAL',  # RayStation supported
            summation_type='PLAN',
            patient_info={'PatientID': 'RAYSTATION_001', 'PatientName': 'RayStation^Compatibility^Test'}
        )
        
        # Save and validate
        dose_path = temp_vendor_dir / "raystation_dose.dcm"
        rt_dose.save(dose_path)
        
        # Load and validate against RayStation requirements
        reloaded_dose = pydicom.dcmread(dose_path)
        validator = VendorCompatibilityValidator()
        issues = validator.validate_vendor_compatibility(reloaded_dose, 'raystation')
        
        # Filter out warnings, focus on errors
        errors = [issue for issue in issues if issue['type'] == 'error']
        assert len(errors) == 0, f"RayStation compatibility errors: {errors}"
        
        # Verify RayStation supports multiple dose types
        supported_dose_types = ['PHYSICAL', 'EFFECTIVE', 'ERROR']
        assert reloaded_dose.DoseType in supported_dose_types, f"RayStation requires dose type in {supported_dose_types}"
        
        # Verify RayStation has more relaxed grid size limits
        dose_shape = reloaded_dose.pixel_array.shape
        raystation_limits = VendorCompatibilityValidator.VENDOR_REQUIREMENTS['raystation']['max_dose_grid_size']
        
        for i, (actual, limit) in enumerate(zip(dose_shape, raystation_limits)):
            assert actual <= limit, f"Dose grid dimension {i} ({actual}) exceeds RayStation limit ({limit})"
        
        print(f"✅ RayStation compatibility validated:")
        print(f"   - Supports multiple dose types: {supported_dose_types}")
        print(f"   - Large grid support: {dose_shape} (limit: {raystation_limits})")
        print(f"   - Flexible pixel spacing requirements")


class TestGenericDicomCompatibility:
    """Test generic DICOM-RT compliance for maximum interoperability."""
    
    def test_generic_dicom_rt_compliance(self, clinical_dataset_suite, temp_vendor_dir):
        """Test RT Dose generic DICOM compliance for broad compatibility."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': 'GENERIC_001', 'PatientName': 'Generic^DICOM^Test'}
        )
        
        ct_dir = temp_vendor_dir / "generic_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Test multiple dose configurations for generic compatibility
        dose_configs = [
            {'dose_units': 'GY', 'dose_type': 'PHYSICAL', 'summation_type': 'PLAN'},
            {'dose_units': 'GY', 'dose_type': 'EFFECTIVE', 'summation_type': 'PLAN'},
            {'dose_units': 'RELATIVE', 'dose_type': 'PHYSICAL', 'summation_type': 'BEAM'}
        ]
        
        for i, config in enumerate(dose_configs):
            rt_dose = RTDose.from_array(
                dose_array=clinical_dataset_suite['dose_array'],
                reference_image=reference_ct,
                dose_units=config['dose_units'],
                dose_type=config['dose_type'],
                summation_type=config['summation_type'],
                patient_info={'PatientID': 'GENERIC_001', 'PatientName': 'Generic^DICOM^Test'}
            )
            
            # Save and validate
            dose_path = temp_vendor_dir / f"generic_dose_{i}.dcm"
            rt_dose.save(dose_path)
            
            # Load and validate against generic DICOM requirements
            reloaded_dose = pydicom.dcmread(dose_path)
            validator = VendorCompatibilityValidator()
            issues = validator.validate_vendor_compatibility(reloaded_dose, 'generic_dicom')
            
            # Should have no errors for generic DICOM compliance
            errors = [issue for issue in issues if issue['type'] == 'error']
            assert len(errors) == 0, f"Generic DICOM compliance errors for config {i}: {errors}"
            
            # Verify configuration was applied correctly
            assert reloaded_dose.DoseUnits == config['dose_units']
            assert reloaded_dose.DoseType == config['dose_type']
            assert reloaded_dose.DoseSummationType == config['summation_type']
            
            print(f"✅ Generic DICOM config {i} validated: {config}")
    
    def test_cross_vendor_interoperability(self, clinical_dataset_suite, temp_vendor_dir):
        """Test that RT Dose files work across multiple vendor requirements."""
        
        # Create CT reference
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': 'INTEROP_001', 'PatientName': 'Interoperability^Test'}
        )
        
        ct_dir = temp_vendor_dir / "interop_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Create RT Dose designed for maximum compatibility
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',  # Universally supported
            dose_type='PHYSICAL',  # Universally supported
            summation_type='PLAN',  # Most common
            patient_info={'PatientID': 'INTEROP_001', 'PatientName': 'Interoperability^Test'}
        )
        
        # Save and test against all vendor requirements
        dose_path = temp_vendor_dir / "interop_dose.dcm"
        rt_dose.save(dose_path)
        reloaded_dose = pydicom.dcmread(dose_path)
        
        validator = VendorCompatibilityValidator()
        vendors_to_test = ['varian_eclipse', 'elekta_monaco', 'raystation', 'generic_dicom']
        
        compatibility_results = {}
        
        for vendor in vendors_to_test:
            issues = validator.validate_vendor_compatibility(reloaded_dose, vendor)
            errors = [issue for issue in issues if issue['type'] == 'error']
            warnings = [issue for issue in issues if issue['type'] == 'warning']
            
            compatibility_results[vendor] = {
                'errors': len(errors),
                'warnings': len(warnings),
                'compatible': len(errors) == 0
            }
            
            # Critical: no errors should exist for any vendor
            assert len(errors) == 0, f"Interoperability failed for {vendor}: {errors}"
        
        # Verify all vendors are compatible
        all_compatible = all(result['compatible'] for result in compatibility_results.values())
        assert all_compatible, f"Not all vendors compatible: {compatibility_results}"
        
        print(f"✅ Cross-vendor interoperability validated:")
        for vendor, result in compatibility_results.items():
            print(f"   - {vendor}: Compatible (errors: {result['errors']}, warnings: {result['warnings']})")


class TestClinicalWorkflowCompatibility:
    """Test clinical workflow compatibility across TPS systems."""
    
    def test_treatment_planning_workflow_validation(self, clinical_dataset_suite, temp_vendor_dir):
        """Test complete treatment planning workflow compatibility."""
        
        # Simulate typical clinical workflow
        patient_id = 'CLINICAL_WORKFLOW_001'
        patient_name = 'Clinical^Workflow^Validation'
        
        # Step 1: Create planning CT
        ct_series = CTSeries.from_array(
            pixel_array=clinical_dataset_suite['ct_array'],
            pixel_spacing=clinical_dataset_suite['geometric_params']['pixel_spacing'],
            slice_thickness=clinical_dataset_suite['geometric_params']['slice_thickness'],
            patient_info={'PatientID': patient_id, 'PatientName': patient_name}
        )
        
        ct_dir = temp_vendor_dir / "clinical_ct"
        ct_dir.mkdir()
        ct_paths = ct_series.save_series(ct_dir)
        reference_ct = pydicom.dcmread(ct_paths[0])
        
        # Step 2: Create RT Structure Set (contouring)
        rt_struct = RTStructureSet.from_masks(
            ct_reference=reference_ct,
            masks=clinical_dataset_suite['structure_masks'],
            patient_info={'PatientID': patient_id, 'PatientName': patient_name}
        )
        
        # Step 3: Create RT Dose (treatment planning)
        rt_dose = RTDose.from_array(
            dose_array=clinical_dataset_suite['dose_array'],
            reference_image=reference_ct,
            dose_units='GY',
            dose_type='PHYSICAL',
            summation_type='PLAN',
            patient_info={'PatientID': patient_id, 'PatientName': patient_name}
        )
        
        # Save complete treatment plan
        struct_path = temp_vendor_dir / "clinical_struct.dcm"
        dose_path = temp_vendor_dir / "clinical_dose.dcm"
        
        rt_struct.save(struct_path)
        rt_dose.save(dose_path)
        
        # Validate complete workflow against all vendors
        reloaded_struct = pydicom.dcmread(struct_path)
        reloaded_dose = pydicom.dcmread(dose_path)
        
        validator = VendorCompatibilityValidator()
        
        # Test RT Dose compatibility with all major TPS systems
        vendors = ['varian_eclipse', 'elekta_monaco', 'raystation']
        workflow_compatibility = {}
        
        for vendor in vendors:
            dose_issues = validator.validate_vendor_compatibility(reloaded_dose, vendor)
            dose_errors = [issue for issue in dose_issues if issue['type'] == 'error']
            
            workflow_compatibility[vendor] = {
                'dose_compatible': len(dose_errors) == 0,
                'dose_issues': len(dose_issues),
                'workflow_ready': len(dose_errors) == 0  # Simplified for RT Dose focus
            }
        
        # Verify workflow compatibility
        all_workflow_ready = all(result['workflow_ready'] for result in workflow_compatibility.values())
        assert all_workflow_ready, f"Clinical workflow not ready for all vendors: {workflow_compatibility}"
        
        # Verify clinical data integrity
        assert reloaded_dose.PatientID == patient_id, "Patient ID consistency required for clinical workflow"
        assert reference_ct.FrameOfReferenceUID == reloaded_dose.FrameOfReferenceUID, "Frame of Reference consistency critical"
        
        print(f"✅ Clinical workflow compatibility validated:")
        print(f"   - Patient: {patient_id}")
        print(f"   - Frame of Reference UID: {reference_ct.FrameOfReferenceUID}")
        for vendor, result in workflow_compatibility.items():
            print(f"   - {vendor}: Workflow ready ({result['dose_issues']} total issues)")


if __name__ == "__main__":
    # Run vendor compatibility tests
    pytest.main([__file__, "-v"])