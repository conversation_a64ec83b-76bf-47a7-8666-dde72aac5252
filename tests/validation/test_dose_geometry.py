"""
Tests for dose geometry validation.

This module tests comprehensive geometric validation for RT Dose objects,
including dose-CT alignment, spatial bounds validation, and coordinate
system consistency checks.
"""

import pytest
import numpy as np
from pydicom import Dataset

from pyrt_dicom.validation.geometric import (
    validate_dose_ct_alignment,
    validate_dose_spatial_bounds,
)
from pyrt_dicom.validation.clinical import ValidationLevel


class TestDoseCTAlignment:
    """Test dose-CT alignment validation."""

    def create_test_ct_dataset(self):
        """Create test CT dataset with standard parameters."""
        ct_ds = Dataset()
        ct_ds.FrameOfReferenceUID = "1.2.3.4.5.6.7"
        ct_ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        ct_ds.ImagePositionPatient = [-256.0, -256.0, -100.0]
        ct_ds.PixelSpacing = [1.0, 1.0]
        ct_ds.Rows = 512
        ct_ds.Columns = 512
        return ct_ds

    def create_test_dose_dataset(self):
        """Create test dose dataset with standard parameters."""
        dose_ds = Dataset()
        dose_ds.FrameOfReferenceUID = "1.2.3.4.5.6.7"
        dose_ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        dose_ds.ImagePositionPatient = [-256.0, -256.0, -100.0]
        dose_ds.PixelSpacing = [2.0, 2.0]
        dose_ds.Rows = 256
        dose_ds.Columns = 256
        dose_ds.NumberOfFrames = 50
        dose_ds.GridFrameOffsetVector = [-100.0 + i * 2.5 for i in range(50)]
        return dose_ds

    def test_perfect_alignment(self):
        """Test validation with perfectly aligned dose and CT."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Make dose exactly aligned with CT
        dose_ds.ImageOrientationPatient = ct_ds.ImageOrientationPatient
        dose_ds.ImagePositionPatient = ct_ds.ImagePositionPatient
        dose_ds.FrameOfReferenceUID = ct_ds.FrameOfReferenceUID

        results = validate_dose_ct_alignment(dose_ds, ct_ds)

        # Should have no errors or warnings for perfect alignment
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

    def test_frame_of_reference_mismatch(self):
        """Test validation with mismatched Frame of Reference UIDs."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Different Frame of Reference UIDs
        dose_ds.FrameOfReferenceUID = "9.8.7.6.5.4.3"

        results = validate_dose_ct_alignment(dose_ds, ct_ds)

        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Frame of Reference UID mismatch" in errors[0].message

    def test_missing_frame_of_reference(self):
        """Test validation with missing Frame of Reference UID."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Remove Frame of Reference UID from dose
        del dose_ds.FrameOfReferenceUID

        results = validate_dose_ct_alignment(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any("missing Frame of Reference UID" in w.message for w in warnings)

    def test_orientation_mismatch(self):
        """Test validation with mismatched image orientations."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Different image orientation
        dose_ds.ImageOrientationPatient = [0.0, 1.0, 0.0, -1.0, 0.0, 0.0]

        results = validate_dose_ct_alignment(dose_ds, ct_ds)

        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 1
        assert "Image orientation mismatch" in errors[0].message

    def test_coarse_dose_grid_warning(self):
        """Test warning for dose grid much coarser than CT."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Make dose grid much coarser than CT
        ct_ds.PixelSpacing = [1.0, 1.0]
        dose_ds.PixelSpacing = [5.0, 5.0]  # 5x coarser

        results = validate_dose_ct_alignment(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any("much coarser than CT" in w.message for w in warnings)

    def test_position_offset_warning(self):
        """Test warning for significant position offset."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Offset dose position by 5mm (exceeds 1mm default tolerance)
        dose_ds.ImagePositionPatient = [-251.0, -251.0, -95.0]

        results = validate_dose_ct_alignment(dose_ds, ct_ds, tolerance_mm=1.0)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any("position offset" in w.message for w in warnings)


class TestDoseSpatialBounds:
    """Test dose spatial bounds validation."""

    def create_test_ct_dataset(self):
        """Create test CT dataset."""
        ct_ds = Dataset()
        ct_ds.ImagePositionPatient = [-256.0, -256.0, -100.0]
        ct_ds.PixelSpacing = [1.0, 1.0]
        ct_ds.Rows = 512
        ct_ds.Columns = 512
        return ct_ds

    def create_test_dose_dataset(self):
        """Create test dose dataset."""
        dose_ds = Dataset()
        dose_ds.ImagePositionPatient = [-128.0, -128.0, -50.0]
        dose_ds.PixelSpacing = [2.0, 2.0]
        dose_ds.Rows = 128
        dose_ds.Columns = 128
        dose_ds.NumberOfFrames = 25
        dose_ds.GridFrameOffsetVector = [-50.0 + i * 4.0 for i in range(25)]
        return dose_ds

    def test_reasonable_bounds(self):
        """Test validation with reasonable dose bounds."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        # Should have no major warnings for reasonable bounds
        errors = [r for r in results if r.level == ValidationLevel.ERROR]
        assert len(errors) == 0

    def test_dose_extends_beyond_ct_x(self):
        """Test warning when dose extends beyond CT in X direction."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Make dose extend far beyond CT in X
        dose_ds.ImagePositionPatient = [
            -400.0,
            -128.0,
            -50.0,
        ]  # Start much further left
        dose_ds.Columns = 200  # Large dose grid

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any(
            "extends" in w.message and "X direction" in w.message for w in warnings
        )

    def test_dose_extends_beyond_ct_y(self):
        """Test warning when dose extends beyond CT in Y direction."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Make dose extend far beyond CT in Y
        dose_ds.ImagePositionPatient = [
            -128.0,
            -400.0,
            -50.0,
        ]  # Start much further down
        dose_ds.Rows = 200  # Large dose grid

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any(
            "extends" in w.message and "Y direction" in w.message for w in warnings
        )

    def test_large_z_extent_info(self):
        """Test info message for very large Z extent."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = self.create_test_dose_dataset()

        # Create very large Z extent (>50cm)
        dose_ds.NumberOfFrames = 220
        dose_ds.GridFrameOffsetVector = [
            -250.0 + i * 2.5 for i in range(220)
        ]  # 55cm extent (>50cm threshold)

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        info_results = [r for r in results if r.level == ValidationLevel.INFO]
        assert len(info_results) >= 1
        assert any("Large dose grid Z extent" in i.message for i in info_results)

    def test_missing_dose_geometry(self):
        """Test handling of missing dose geometry parameters."""
        ct_ds = self.create_test_ct_dataset()
        dose_ds = Dataset()  # Empty dose dataset

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any(
            "Insufficient dose geometric parameters" in w.message for w in warnings
        )

    def test_missing_ct_geometry(self):
        """Test handling of missing CT geometry parameters."""
        ct_ds = Dataset()  # Empty CT dataset
        dose_ds = self.create_test_dose_dataset()

        results = validate_dose_spatial_bounds(dose_ds, ct_ds)

        warnings = [r for r in results if r.level == ValidationLevel.WARNING]
        assert len(warnings) >= 1
        assert any(
            "Insufficient CT geometric parameters" in w.message for w in warnings
        )
